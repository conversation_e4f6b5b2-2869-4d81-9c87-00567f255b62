#!/bin/bash

# Enhanced Server Analysis and Dockerfile Generator
# Gera uma análise completa do servidor para replicação fiel via Docker

# Solicitar nome do container
echo "=== Enhanced Server Analysis and Dockerfile Generator ==="
echo ""
read -p "Digite o nome do container: " CONTAINER_NAME

# Validar nome do container
if [[ -z "$CONTAINER_NAME" ]]; then
    echo "Erro: Nome do container não pode estar vazio!"
    exit 1
fi

# Criar diretório para o container
CONTAINER_DIR="$CONTAINER_NAME"
if [[ -d "$CONTAINER_DIR" ]]; then
    echo "Diretório '$CONTAINER_DIR' já existe. Deseja continuar? (y/N)"
    read -p "> " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Operação cancelada."
        exit 1
    fi
fi

mkdir -p "$CONTAINER_DIR"
cd "$CONTAINER_DIR"

echo "Criando arquivos no diretório: $(pwd)"
echo ""

# Define os nomes dos arquivos de saída
DOCKERFILE="output.Dockerfile"
INFOFILE="docker_info.log"
PACKAGES_FILE="detected_packages.txt"
SERVICES_FILE="detected_services.txt"
CRONTAB_FILE="detected_crontabs.txt"
CONFIGS_FILE="detected_configs.txt"
CUSTOM_PACKAGES_FILE="custom_packages.txt"
APP_DIRS_FILE="application_directories.txt"

# Limpar arquivos anteriores
> "$INFOFILE"
> "$PACKAGES_FILE"
> "$SERVICES_FILE"
> "$CRONTAB_FILE"
> "$CONFIGS_FILE"
> "$CUSTOM_PACKAGES_FILE"
> "$APP_DIRS_FILE"

echo "=== Iniciando análise completa do servidor ===" | tee -a "$INFOFILE"
echo "Container: $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "Data/Hora: $(date)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Sistema Operacional
# -----------------------------
echo "=== Detectando Sistema Operacional ===" | tee -a "$INFOFILE"

OS_ID=""
OS_VER=""
if [ -f /etc/os-release ]; then
  OS_ID=$(awk -F= '/^ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  OS_VER=$(awk -F= '/^VERSION_ID=/{gsub(/"/,"",$2); print $2}' /etc/os-release || echo "")
  echo "Sistema: $OS_ID $OS_VER" | tee -a "$INFOFILE"
fi

# Determinar base image
BASE_IMAGE="centos:7"
if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]] && [ -n "$OS_VER" ]; then
  if [[ "$OS_VER" == "8" ]]; then
    BASE_IMAGE="rockylinux:8"
  elif [[ "$OS_VER" == "9" ]]; then
    BASE_IMAGE="rockylinux:9"
  else
    BASE_IMAGE="centos:${OS_VER}"
  fi
elif [[ "$OS_ID" =~ fedora ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="fedora:${OS_VER}"
elif [[ "$OS_ID" =~ ubuntu ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="ubuntu:${OS_VER}"
elif [[ "$OS_ID" =~ debian ]] && [ -n "$OS_VER" ]; then
  BASE_IMAGE="debian:${OS_VER}"
fi

echo "Base image selecionada: $BASE_IMAGE" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Lista de Pacotes Mínimos (Base System)
# -----------------------------
BASE_PACKAGES=(
  "bash" "coreutils" "util-linux" "systemd" "glibc" "yum" "rpm" 
  "filesystem" "setup" "basesystem" "tzdata" "rootfiles" "vim-minimal"
  "procps-ng" "kmod" "systemd-libs" "dbus" "dbus-libs" "kernel"
  "initscripts" "chkconfig" "service" "which" "findutils" "grep"
  "sed" "gawk" "tar" "gzip" "bzip2" "xz" "less" "shadow-utils"
  "passwd" "cronie" "logrotate" "rsyslog" "ca-certificates"
  "curl" "wget" "openssh" "openssh-server" "openssh-clients"
  "net-tools" "iproute" "iputils" "bind-utils" "telnet"
  "centos-release" "redhat-release" "system-release"
)

# -----------------------------
# Detectar Pacotes Instalados
# -----------------------------
echo "=== Coletando lista de pacotes instalados ===" | tee -a "$INFOFILE"

if command -v rpm >/dev/null 2>&1; then
  echo "Sistema baseado em RPM detectado" | tee -a "$INFOFILE"
  
  # Todos os pacotes instalados
  rpm -qa --qf "%{NAME}\n" | sort > "$PACKAGES_FILE"
  TOTAL_PACKAGES=$(wc -l < "$PACKAGES_FILE")
  echo "Total de pacotes instalados: $TOTAL_PACKAGES" | tee -a "$INFOFILE"
  
  # Detectar pacotes personalizados (não são do sistema base)
  echo "=== Detectando pacotes personalizados ===" | tee -a "$INFOFILE"
  
  while read -r pkg; do
    # Verificar se não é um pacote base
    is_base=false
    for base_pkg in "${BASE_PACKAGES[@]}"; do
      if [[ "$pkg" == "$base_pkg"* ]]; then
        is_base=true
        break
      fi
    done
    
    if [ "$is_base" = false ]; then
      # Verificar se é um pacote interessante (serviços, desenvolvimento, etc.)
      if [[ "$pkg" =~ ^(httpd|nginx|apache|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|kubernetes|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|ansible|terraform|vim|emacs|nano|htop|tmux|screen|zsh|fish|certbot|letsencrypt|firewalld|iptables|fail2ban|ntp|chrony|samba|nfs|ftp|vsftpd|postfix|dovecot|bind|named|squid|haproxy|keepalived|pacemaker|corosync|rsync|backup|borg|rclone|awscli|azure-cli|google-cloud|kubectl|helm|prometheus|grafana|zabbix|nagios|icinga|collectd|telegraf|influxdb|gcc|make|cmake|autoconf|automake|libtool|pkgconfig|rpm-build|mock|createrepo|yum-utils|epel-release|remi-release|ius-release|webtatic-release) ]]; then
        echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
      fi
    fi
  done < "$PACKAGES_FILE"
  
  # Detectar repositórios adicionais
  echo "=== Repositórios habilitados ===" | tee -a "$INFOFILE"
  if command -v yum >/dev/null 2>&1; then
    yum repolist enabled 2>/dev/null | grep -v "^repo" | grep -v "^Loaded" | grep -v "^repolist" | tee -a "$INFOFILE"
  fi

elif command -v dpkg >/dev/null 2>&1; then
  echo "Sistema baseado em DEB detectado" | tee -a "$INFOFILE"
  dpkg-query -W -f='${Package}\n' | sort > "$PACKAGES_FILE"
  
  # Para sistemas Debian/Ubuntu, detectar pacotes não essenciais
  while read -r pkg; do
    if [[ "$pkg" =~ ^(apache2|nginx|mysql|mariadb|postgresql|php|python|nodejs|java|git|docker|redis|memcached|elasticsearch|mongodb|rabbitmq|tomcat|jenkins|certbot|ufw|fail2ban|ntp|samba|nfs|vsftpd|postfix|dovecot|bind9|squid|haproxy|rsync|awscli|kubectl) ]]; then
      echo "$pkg" >> "$CUSTOM_PACKAGES_FILE"
    fi
  done < "$PACKAGES_FILE"
fi

CUSTOM_PACKAGES_COUNT=$(wc -l < "$CUSTOM_PACKAGES_FILE" 2>/dev/null || echo "0")
echo "Pacotes personalizados detectados: $CUSTOM_PACKAGES_COUNT" | tee -a "$INFOFILE"

if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
  echo "Principais pacotes personalizados:" | tee -a "$INFOFILE"
  head -20 "$CUSTOM_PACKAGES_FILE" | while read -r pkg; do
    echo "  - $pkg" | tee -a "$INFOFILE"
  done
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar e Filtrar Serviços Habilitados
# -----------------------------
echo "=== Coletando e filtrando serviços habilitados ===" | tee -a "$INFOFILE"

# Serviços a serem ignorados (comuns em todos os containers)
IGNORED_SERVICES=(
  "bacula-fd"
  "getty@"
  "getty"
  "nslcd"
  "qemu"
  "systemd-"
  "sshd"
  "zabbix-agent"
  "NetworkManager"
  "dbus"
  "chronyd"
  "crond"
  "rsyslog"
  "firewalld"
  "iptables"
  "network"
  "auditd"
  "tuned"
  "irqbalance"
  "rngd"
  "lvm2"
  "multipathd"
)

# Função para verificar se um serviço deve ser ignorado
should_ignore_service() {
  local service="$1"
  for ignored in "${IGNORED_SERVICES[@]}"; do
    if [[ "$service" == *"$ignored"* ]]; then
      return 0  # Deve ignorar
    fi
  done
  return 1  # Não deve ignorar
}

if command -v systemctl >/dev/null 2>&1; then
  # Coletar todos os serviços habilitados
  ALL_SERVICES_FILE="all_services.txt"
  systemctl list-unit-files --type=service --state=enabled --no-pager --no-legend | awk '{print $1}' > "$ALL_SERVICES_FILE"

  # Filtrar serviços relevantes
  > "$SERVICES_FILE"
  while read -r service; do
    if ! should_ignore_service "$service"; then
      echo "$service" >> "$SERVICES_FILE"
    fi
  done < "$ALL_SERVICES_FILE"

  TOTAL_SERVICES=$(wc -l < "$ALL_SERVICES_FILE")
  RELEVANT_SERVICES=$(wc -l < "$SERVICES_FILE")

  echo "Total de serviços habilitados: $TOTAL_SERVICES" | tee -a "$INFOFILE"
  echo "Serviços relevantes (filtrados): $RELEVANT_SERVICES" | tee -a "$INFOFILE"
  echo "" | tee -a "$INFOFILE"

  if [ -s "$SERVICES_FILE" ]; then
    echo "Serviços relevantes encontrados:" | tee -a "$INFOFILE"
    while read -r service; do
      echo "  - $service" | tee -a "$INFOFILE"
    done < "$SERVICES_FILE"
  else
    echo "Nenhum serviço relevante encontrado" | tee -a "$INFOFILE"
  fi

elif command -v chkconfig >/dev/null 2>&1; then
  chkconfig --list 2>/dev/null | grep ":on" | awk '{print $1}' | while read -r service; do
    if ! should_ignore_service "$service"; then
      echo "$service" >> "$SERVICES_FILE"
    fi
  done
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Analisar Arquivos .service para Descobrir Diretórios
# -----------------------------
echo "=== Analisando arquivos .service para descobrir diretórios ===" | tee -a "$INFOFILE"

SERVICE_DIRS_FILE="service_directories.txt"
> "$SERVICE_DIRS_FILE"

if [ -s "$SERVICES_FILE" ]; then
    echo "Analisando arquivos .service dos serviços relevantes..." | tee -a "$INFOFILE"

    while read -r service; do
        # Encontrar o arquivo .service
        service_file=""
        for service_dir in /etc/systemd/system /usr/lib/systemd/system /lib/systemd/system; do
            if [ -f "$service_dir/$service" ]; then
                service_file="$service_dir/$service"
                break
            fi
        done

        if [ -n "$service_file" ] && [ -f "$service_file" ]; then
            echo "  Analisando $service..." | tee -a "$INFOFILE"

            # Extrair diretórios de ExecStart, ExecReload, WorkingDirectory, etc.
            grep -E "^(ExecStart|ExecReload|ExecStop|WorkingDirectory|Environment|EnvironmentFile)" "$service_file" | while read -r line; do
                # Extrair caminhos da linha
                echo "$line" | grep -oE '/[a-zA-Z0-9/_.-]+' | while read -r path; do
                    if [[ "$path" =~ ^/[a-zA-Z] ]] && [[ ! "$path" =~ ^/(bin|sbin|usr/bin|usr/sbin|tmp|var/tmp|dev|proc|sys)/ ]]; then
                        # Se é um arquivo, pegar o diretório pai
                        if [ -f "$path" ]; then
                            dirname "$path" >> "$SERVICE_DIRS_FILE"
                        # Se é um diretório, adicionar diretamente
                        elif [ -d "$path" ]; then
                            echo "$path" >> "$SERVICE_DIRS_FILE"
                        # Se não existe, mas parece um caminho válido, pegar o diretório pai
                        else
                            dirname "$path" >> "$SERVICE_DIRS_FILE"
                        fi
                    fi
                done
            done
        fi
    done < "$SERVICES_FILE"

    # Remover duplicatas
    if [ -s "$SERVICE_DIRS_FILE" ]; then
        sort -u "$SERVICE_DIRS_FILE" -o "$SERVICE_DIRS_FILE"
        SERVICE_DIRS_COUNT=$(wc -l < "$SERVICE_DIRS_FILE")
        echo "Diretórios descobertos nos arquivos .service: $SERVICE_DIRS_COUNT" | tee -a "$INFOFILE"

        echo "Diretórios encontrados nos serviços:" | tee -a "$INFOFILE"
        while read -r dir; do
            echo "  - $dir" | tee -a "$INFOFILE"
        done < "$SERVICE_DIRS_FILE"
    else
        echo "Nenhum diretório específico encontrado nos arquivos .service" | tee -a "$INFOFILE"
    fi
fi

echo "" | tee -a "$INFOFILE"

# -----------------------------
# Descobrir Diretórios de Aplicações (Baseado em Análise)
# -----------------------------
echo "=== Descobrindo diretórios de aplicações baseado em análise ===" | tee -a "$INFOFILE"

# Diretórios obrigatórios - apenas os essenciais para web e bancos
MANDATORY_DIRS=(
    "/var/www"
    "/var/lib/mysql"
    "/var/lib/postgresql"
    "/var/lib/pgsql"
    "/var/lib/redis"
    "/var/lib/mongodb"
)

echo "Adicionando diretórios obrigatórios (apenas essenciais)..." | tee -a "$INFOFILE"
for dir in "${MANDATORY_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "$dir" >> "$APP_DIRS_FILE"
        echo "  Obrigatório: $dir" | tee -a "$INFOFILE"
    fi
done

# Adicionar diretórios descobertos na crontab
if [ -s "$CRONTAB_DIRS_FILE" ]; then
    echo "Adicionando diretórios descobertos na crontab..." | tee -a "$INFOFILE"
    while read -r dir; do
        if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
            echo "$dir" >> "$APP_DIRS_FILE"
            echo "  Da crontab: $dir" | tee -a "$INFOFILE"
        fi
    done < "$CRONTAB_DIRS_FILE"
fi

# Adicionar diretórios descobertos nos arquivos .service
if [ -s "$SERVICE_DIRS_FILE" ]; then
    echo "Adicionando diretórios descobertos nos serviços..." | tee -a "$INFOFILE"
    while read -r dir; do
        if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
            echo "$dir" >> "$APP_DIRS_FILE"
            echo "  Do serviço: $dir" | tee -a "$INFOFILE"
        fi
    done < "$SERVICE_DIRS_FILE"
fi

# Buscar diretórios específicos apenas em /opt, /srv se houver serviços apontando para eles
echo "Verificando diretórios específicos em /opt e /srv..." | tee -a "$INFOFILE"

# Verificar se algum serviço ou crontab aponta para /opt ou /srv
SPECIFIC_DIRS_TO_CHECK=()

if [ -s "$SERVICE_DIRS_FILE" ] || [ -s "$CRONTAB_DIRS_FILE" ]; then
    # Verificar /opt
    if ([ -s "$SERVICE_DIRS_FILE" ] && grep -q "^/opt/" "$SERVICE_DIRS_FILE") || \
       ([ -s "$CRONTAB_DIRS_FILE" ] && grep -q "^/opt/" "$CRONTAB_DIRS_FILE"); then
        echo "Serviços encontrados em /opt, incluindo diretórios específicos..." | tee -a "$INFOFILE"

        # Adicionar apenas os diretórios específicos em /opt que foram referenciados
        if [ -s "$SERVICE_DIRS_FILE" ]; then
            grep "^/opt/" "$SERVICE_DIRS_FILE" | while read -r dir; do
                if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
                    echo "$dir" >> "$APP_DIRS_FILE"
                    echo "  Específico em /opt: $dir" | tee -a "$INFOFILE"
                fi
            done
        fi

        if [ -s "$CRONTAB_DIRS_FILE" ]; then
            grep "^/opt/" "$CRONTAB_DIRS_FILE" | while read -r dir; do
                if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
                    echo "$dir" >> "$APP_DIRS_FILE"
                    echo "  Específico em /opt: $dir" | tee -a "$INFOFILE"
                fi
            done
        fi
    fi

    # Verificar /srv
    if ([ -s "$SERVICE_DIRS_FILE" ] && grep -q "^/srv/" "$SERVICE_DIRS_FILE") || \
       ([ -s "$CRONTAB_DIRS_FILE" ] && grep -q "^/srv/" "$CRONTAB_DIRS_FILE"); then
        echo "Serviços encontrados em /srv, incluindo diretórios específicos..." | tee -a "$INFOFILE"

        # Adicionar apenas os diretórios específicos em /srv que foram referenciados
        if [ -s "$SERVICE_DIRS_FILE" ]; then
            grep "^/srv/" "$SERVICE_DIRS_FILE" | while read -r dir; do
                if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
                    echo "$dir" >> "$APP_DIRS_FILE"
                    echo "  Específico em /srv: $dir" | tee -a "$INFOFILE"
                fi
            done
        fi

        if [ -s "$CRONTAB_DIRS_FILE" ]; then
            grep "^/srv/" "$CRONTAB_DIRS_FILE" | while read -r dir; do
                if [ -d "$dir" ] && ! grep -Fxq "$dir" "$APP_DIRS_FILE" 2>/dev/null; then
                    echo "$dir" >> "$APP_DIRS_FILE"
                    echo "  Específico em /srv: $dir" | tee -a "$INFOFILE"
                fi
            done
        fi
    fi
fi

# Remover duplicatas e ordenar
if [ -s "$APP_DIRS_FILE" ]; then
    sort -u "$APP_DIRS_FILE" -o "$APP_DIRS_FILE"
    APP_DIRS_COUNT=$(wc -l < "$APP_DIRS_FILE")
    echo "Total de diretórios selecionados: $APP_DIRS_COUNT" | tee -a "$INFOFILE"

    echo "Diretórios finais selecionados para backup:" | tee -a "$INFOFILE"
    while read -r dir; do
        echo "  - $dir" | tee -a "$INFOFILE"
    done < "$APP_DIRS_FILE"
else
    echo "Nenhum diretório de aplicação encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Coletar e Analisar Crontab (apenas root)
# -----------------------------
echo "=== Coletando e analisando crontab do root ===" | tee -a "$INFOFILE"

# Apenas crontab do root, preservando comentários
echo "# ROOT CRONTAB - Coletado em $(date)" > "$CRONTAB_FILE"
echo "# Servidor: $(hostname)" >> "$CRONTAB_FILE"
echo "" >> "$CRONTAB_FILE"

CRONTAB_DIRS_FILE="crontab_directories.txt"
> "$CRONTAB_DIRS_FILE"

# Tentar diferentes métodos para obter a crontab do root
CRONTAB_FOUND=false
if crontab -l -u root 2>/dev/null >> "$CRONTAB_FILE"; then
    echo "Crontab do root coletada via 'crontab -l'" | tee -a "$INFOFILE"
    CRONTAB_FOUND=true
elif [ -f /var/spool/cron/root ]; then
    cat /var/spool/cron/root >> "$CRONTAB_FILE"
    echo "Crontab do root coletada de /var/spool/cron/root" | tee -a "$INFOFILE"
    CRONTAB_FOUND=true
elif [ -f /var/spool/cron/crontabs/root ]; then
    cat /var/spool/cron/crontabs/root >> "$CRONTAB_FILE"
    echo "Crontab do root coletada de /var/spool/cron/crontabs/root" | tee -a "$INFOFILE"
    CRONTAB_FOUND=true
else
    echo "# Nenhuma crontab encontrada para o usuário root" >> "$CRONTAB_FILE"
    echo "Nenhuma crontab encontrada para o usuário root" | tee -a "$INFOFILE"
fi

# Analisar crontab para descobrir diretórios de aplicações
if [ "$CRONTAB_FOUND" = true ]; then
    echo "Analisando crontab para descobrir diretórios de aplicações..." | tee -a "$INFOFILE"

    # Extrair caminhos de scripts e comandos da crontab
    grep -v "^#" "$CRONTAB_FILE" | grep -v "^$" | while read -r line; do
        # Extrair caminhos de arquivos/scripts da linha
        echo "$line" | grep -oE '/[a-zA-Z0-9/_.-]+' | while read -r path; do
            if [[ "$path" =~ ^/[a-zA-Z] ]] && [[ ! "$path" =~ ^/(bin|sbin|usr/bin|usr/sbin|tmp|var/tmp|dev|proc|sys)/ ]]; then
                # Se é um arquivo, pegar o diretório pai
                if [ -f "$path" ]; then
                    dirname "$path" >> "$CRONTAB_DIRS_FILE"
                # Se é um diretório, adicionar diretamente
                elif [ -d "$path" ]; then
                    echo "$path" >> "$CRONTAB_DIRS_FILE"
                # Se não existe, mas parece um caminho válido, pegar o diretório pai
                else
                    dirname "$path" >> "$CRONTAB_DIRS_FILE"
                fi
            fi
        done
    done

    # Remover duplicatas e mostrar diretórios encontrados
    if [ -s "$CRONTAB_DIRS_FILE" ]; then
        sort -u "$CRONTAB_DIRS_FILE" -o "$CRONTAB_DIRS_FILE"
        CRONTAB_DIRS_COUNT=$(wc -l < "$CRONTAB_DIRS_FILE")
        echo "Diretórios descobertos na crontab: $CRONTAB_DIRS_COUNT" | tee -a "$INFOFILE"

        echo "Diretórios encontrados na crontab:" | tee -a "$INFOFILE"
        while read -r dir; do
            echo "  - $dir" | tee -a "$INFOFILE"
        done < "$CRONTAB_DIRS_FILE"
    else
        echo "Nenhum diretório específico encontrado na crontab" | tee -a "$INFOFILE"
    fi
fi

echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Configurações Importantes (Expandido)
# -----------------------------
echo "=== Detectando arquivos de configuração importantes ===" | tee -a "$INFOFILE"

# Função para buscar arquivos de configuração em diretórios
find_config_files() {
    local search_dir="$1"
    local pattern="$2"

    if [ -d "$search_dir" ]; then
        find "$search_dir" -name "$pattern" -type f 2>/dev/null
    fi
}

# Configurações específicas dos serviços mais comuns
echo "Buscando configurações dos serviços principais..." | tee -a "$INFOFILE"

# Apache/HTTPD - Configurações e VirtualHosts
echo "  - Apache/HTTPD..." | tee -a "$INFOFILE"
for apache_dir in /etc/httpd /etc/apache2; do
    if [ -d "$apache_dir" ]; then
        # Configuração principal
        find_config_files "$apache_dir" "*.conf" >> "$CONFIGS_FILE"

        # Virtualhosts - TODOS os arquivos, não apenas .conf
        find_config_files "$apache_dir/sites-available" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/sites-enabled" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf.d" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf-available" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/conf-enabled" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/vhosts.d" "*" >> "$CONFIGS_FILE"
        find_config_files "$apache_dir/extra" "*.conf" >> "$CONFIGS_FILE"
    fi
done

# Nginx - Configurações e Sites
echo "  - Nginx..." | tee -a "$INFOFILE"
for nginx_dir in /etc/nginx; do
    if [ -d "$nginx_dir" ]; then
        find_config_files "$nginx_dir" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/sites-available" "*" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/sites-enabled" "*" >> "$CONFIGS_FILE"
        find_config_files "$nginx_dir/conf.d" "*" >> "$CONFIGS_FILE"
    fi
done

# PHP - Todas as versões
echo "  - PHP..." | tee -a "$INFOFILE"
for php_dir in /etc/php* /etc/php /usr/local/etc/php*; do
    if [ -d "$php_dir" ]; then
        find_config_files "$php_dir" "php.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir" "*.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/conf.d" "*.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/fpm" "*.conf" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/apache2" "*.ini" >> "$CONFIGS_FILE"
        find_config_files "$php_dir/cli" "*.ini" >> "$CONFIGS_FILE"
    fi
done

# Lista de configurações específicas dos serviços da infraestrutura
IMPORTANT_CONFIGS=(
    # Bancos de dados
    "/etc/my.cnf"
    "/etc/mysql/my.cnf"
    "/etc/mysql/mysql.conf.d/*.cnf"
    "/etc/mysql/conf.d/*.cnf"
    "/etc/postgresql/*/main/postgresql.conf"
    "/etc/postgresql/*/main/pg_hba.conf"
    "/var/lib/pgsql/data/postgresql.conf"
    "/var/lib/pgsql/data/pg_hba.conf"
    "/etc/redis.conf"
    "/etc/redis/redis.conf"
    "/etc/mongodb.conf"
    "/etc/mongod.conf"

    # Mail (Postfix/Dovecot)
    "/etc/postfix/main.cf"
    "/etc/postfix/master.cf"
    "/etc/postfix/*.cf"
    "/etc/dovecot/dovecot.conf"
    "/etc/dovecot/conf.d/*.conf"

    # Zabbix
    "/etc/zabbix/*.conf"
    "/usr/local/etc/zabbix/*.conf"
    "/opt/zabbix/etc/*.conf"

    # Grafana
    "/etc/grafana/*.ini"
    "/etc/grafana/*.conf"
    "/usr/local/etc/grafana/*.ini"
    "/opt/grafana/conf/*.ini"

    # Radius
    "/etc/raddb/*.conf"
    "/etc/freeradius/*.conf"
    "/usr/local/etc/raddb/*.conf"

    # Sistema
    "/etc/ssh/sshd_config"
    "/etc/sudoers"
    "/etc/sudoers.d/*"
    "/etc/hosts"
    "/etc/resolv.conf"
    "/etc/fstab"
    "/etc/exports"
    "/etc/samba/smb.conf"

    # DNS
    "/etc/bind/named.conf"
    "/etc/bind/named.conf.local"
    "/etc/bind/named.conf.options"
    "/etc/bind/db.*"

    # Firewall/Segurança
    "/etc/firewalld/zones/*.xml"
    "/etc/iptables/rules.v4"
    "/etc/iptables/rules.v6"
    "/etc/fail2ban/jail.local"
    "/etc/fail2ban/jail.d/*.conf"

    # Sistema/Logs
    "/etc/logrotate.d/*"
    "/etc/rsyslog.conf"
    "/etc/rsyslog.d/*.conf"
    "/etc/systemd/system/*.service"
    "/etc/cron.d/*"
    "/etc/environment"
    "/etc/profile.d/*.sh"

    # Plesk (se existir)
    "/opt/psa/etc/*.conf"
    "/usr/local/psa/etc/*.conf"

    # Netbox
    "/opt/netbox/netbox/netbox/configuration.py"
    "/etc/netbox/*.py"
)

# Buscar todos os arquivos de configuração
for config in "${IMPORTANT_CONFIGS[@]}"; do
    for file in $config; do
        if [ -f "$file" ]; then
            echo "$file" >> "$CONFIGS_FILE"
        fi
    done
done

# Buscar configurações específicas nos diretórios de aplicações descobertos
if [ -s "$APP_DIRS_FILE" ]; then
    echo "Buscando configurações específicas nos diretórios de aplicações..." | tee -a "$INFOFILE"
    while read -r app_dir; do
        if [ -d "$app_dir" ]; then
            # Apenas arquivos de configuração específicos, evitando logs e temporários
            find_config_files "$app_dir" "*.conf" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.ini" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "*.cfg" >> "$CONFIGS_FILE"

            # Arquivos específicos de aplicações conhecidas
            find_config_files "$app_dir" ".env" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" ".env.local" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" ".env.production" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "config.php" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "config.py" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "settings.py" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "configuration.py" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "app.conf" >> "$CONFIGS_FILE"
            find_config_files "$app_dir" "application.conf" >> "$CONFIGS_FILE"
        fi
    done < "$APP_DIRS_FILE"
fi

# Filtrar e organizar arquivos de configuração
if [ -s "$CONFIGS_FILE" ]; then
    echo "Filtrando arquivos de configuração..." | tee -a "$INFOFILE"

    # Filtrar arquivos muito grandes (> 5MB), logs, e arquivos temporários
    temp_configs=$(mktemp)
    while read -r config_file; do
        if [ -f "$config_file" ]; then
            file_size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "0")

            # Filtrar por tamanho e extensão
            if [ "$file_size" -lt 5242880 ] && \
               [[ ! "$config_file" =~ \.(log|LOG|tmp|temp|cache|pid|lock)$ ]] && \
               [[ ! "$config_file" =~ /(log|logs|tmp|temp|cache|pid|lock)/ ]]; then
                echo "$config_file" >> "$temp_configs"
            fi
        fi
    done < "$CONFIGS_FILE"

    # Remover duplicatas e ordenar
    sort -u "$temp_configs" -o "$CONFIGS_FILE"
    rm -f "$temp_configs"

    CONFIG_COUNT=$(wc -l < "$CONFIGS_FILE")
    echo "Arquivos de configuração selecionados: $CONFIG_COUNT" | tee -a "$INFOFILE"

    if [ "$CONFIG_COUNT" -gt 0 ]; then
        echo "Principais arquivos de configuração:" | tee -a "$INFOFILE"
        head -30 "$CONFIGS_FILE" | while read -r config; do
            echo "  - $config" | tee -a "$INFOFILE"
        done

        if [ "$CONFIG_COUNT" -gt 30 ]; then
            echo "  ... e mais $((CONFIG_COUNT - 30)) arquivos" | tee -a "$INFOFILE"
        fi
    fi
else
    echo "Nenhum arquivo de configuração encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Usuários do Sistema
# -----------------------------
echo "=== Coletando usuários do sistema ===" | tee -a "$INFOFILE"
USERFILE="detected_users.txt"

awk -F: '$3 >= 1000 && $3 < 65534 {print $1":"$3":"$6":"$7}' /etc/passwd > "$USERFILE"
if [ -s "$USERFILE" ]; then
  echo "Usuários personalizados encontrados:" | tee -a "$INFOFILE"
  cat "$USERFILE" | while IFS=: read -r uname uid home shell; do
    echo "  - $uname (UID: $uid, Home: $home, Shell: $shell)" | tee -a "$INFOFILE"
  done
else
  echo "Nenhum usuário personalizado encontrado" | tee -a "$INFOFILE"
fi
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Preparar lista final de diretórios para backup
# -----------------------------
echo "=== Preparando lista final de diretórios para backup ===" | tee -a "$INFOFILE"

# Usar apenas os diretórios descobertos pela análise inteligente
DEPLOY_DIRS=()

# Adicionar diretórios de aplicação descobertos
if [ -s "$APP_DIRS_FILE" ]; then
    while read -r app_dir; do
        if [ -d "$app_dir" ]; then
            DEPLOY_DIRS+=("$app_dir")
        fi
    done < "$APP_DIRS_FILE"
fi

# Adicionar /etc apenas se não estiver na lista (para configurações do sistema)
if [ -d "/etc" ]; then
    found_etc=false
    for dir in "${DEPLOY_DIRS[@]}"; do
        if [ "$dir" = "/etc" ]; then
            found_etc=true
            break
        fi
    done
    if [ "$found_etc" = false ]; then
        DEPLOY_DIRS+=("/etc")
        echo "Adicionando /etc para configurações do sistema" | tee -a "$INFOFILE"
    fi
fi

echo "Diretórios finais selecionados para backup:" | tee -a "$INFOFILE"
for dir in "${DEPLOY_DIRS[@]}"; do
    echo "  - $dir" | tee -a "$INFOFILE"
done
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Detectar Portas em Uso
# -----------------------------
echo "=== Detectando portas em uso ===" | tee -a "$INFOFILE"

if command -v ss >/dev/null 2>&1; then
  PORTS=$(ss -tuln | awk 'NR>1 {gsub(/.*:/,"",$5); print $5}' | sort -n -u | tr '\n' ' ')
else
  PORTS=$(netstat -tuln 2>/dev/null | awk 'NR>2 {gsub(/.*:/,"",$4); print $4}' | sort -n -u | tr '\n' ' ')
fi

echo "Portas detectadas: $PORTS" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# -----------------------------
# Gerar Dockerfile Avançado
# -----------------------------
echo "=== Gerando Dockerfile avançado ===" | tee -a "$INFOFILE"

{
  echo "# Generated by Enhanced Server Analysis Script"
  echo "# Date: $(date)"
  echo "# Base System: $OS_ID $OS_VER"
  echo "# Custom Packages: $CUSTOM_PACKAGES_COUNT"
  echo ""
  echo "FROM ${BASE_IMAGE}"
  echo ""
  echo "LABEL maintainer=\"server-replica@local\""
  echo "LABEL description=\"Replica of production server $(hostname)\""
  echo "LABEL created=\"$(date -I)\""
  echo ""
  echo "ENV container docker"
  echo "ENV DEBIAN_FRONTEND=noninteractive"
  echo ""

  # Instalar repositórios adicionais primeiro
  if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
    echo "# Instalar repositórios adicionais"
    echo "RUN yum -y update && yum -y install epel-release yum-utils"
    
    # Detectar se tem repositórios Remi, IUS, etc.
    if rpm -qa | grep -q remi-release; then
      echo "RUN yum -y install https://rpms.remirepo.net/enterprise/remi-release-\$(rpm -E %rhel).rpm"
    fi
    if rpm -qa | grep -q ius-release; then
      echo "RUN yum -y install https://repo.ius.io/ius-release-el\$(rpm -E %rhel).rpm"
    fi
    echo ""
  fi

  # Instalar pacotes personalizados
  if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
    echo "# Instalar pacotes personalizados detectados"
    
    if [[ "$OS_ID" =~ centos|rhel|rocky|almalinux ]]; then
      # Agrupar pacotes em chunks para evitar linhas muito longas
      CHUNK_SIZE=10
      i=0
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        if [ $((i % CHUNK_SIZE)) -eq 0 ]; then
          if [ $i -gt 0 ]; then
            echo " && yum clean all"
          fi
          echo -n "RUN yum -y install $pkg"
        else
          echo -n " $pkg"
        fi
        i=$((i+1))
      done < "$CUSTOM_PACKAGES_FILE"
      
      if [ $i -gt 0 ]; then
        echo " && yum clean all"
      fi
      
    elif [[ "$OS_ID" =~ ubuntu|debian ]]; then
      echo "RUN apt-get update && apt-get install -y \\"
      while IFS= read -r pkg && [ -n "$pkg" ]; do
        echo "    $pkg \\"
      done < "$CUSTOM_PACKAGES_FILE"
      echo " && apt-get clean && rm -rf /var/lib/apt/lists/*"
    fi
    echo ""
  fi

  # Criar usuários
  if [ -s "$USERFILE" ]; then
    echo "# Criar usuários detectados"
    while IFS=: read -r uname uid home shell; do
      [ -z "$uname" ] && continue
      echo "RUN groupadd -g ${uid} ${uname} 2>/dev/null || true"
      echo "RUN useradd -m -u ${uid} -g ${uid} -s ${shell} -d ${home} ${uname} 2>/dev/null || true"
    done < "$USERFILE"
    echo ""
  fi

  # Configurar crontab do root
  if [ -s "$CRONTAB_FILE" ]; then
    echo "# Configurar crontab do root"
    echo "RUN yum -y install cronie || apt-get update && apt-get install -y cron"
    echo "COPY crontabs.txt /tmp/crontabs.txt"
    echo "RUN crontab -u root /tmp/crontabs.txt 2>/dev/null || true"
    echo ""
  fi

  # Copiar diretórios importantes
  echo "# Copiar diretórios de aplicação e configuração"
  for d in "${DEPLOY_DIRS[@]}"; do
    for realdir in $d; do
      if [ -d "$realdir" ]; then
        # Verificar se o diretório tem conteúdo relevante
        if [ "$(find "$realdir" -type f | wc -l)" -gt 0 ]; then
          echo "COPY ./backup$(echo $realdir) $realdir/"
        fi
      fi
    done
  done
  echo ""

  # Configurar serviços
  if [ -s "$SERVICES_FILE" ]; then
    echo "# Habilitar serviços detectados"
    while read -r service; do
      if [[ "$service" != *"@"* ]] && [[ "$service" != "getty"* ]]; then
        echo "RUN systemctl enable $service 2>/dev/null || true"
      fi
    done < "$SERVICES_FILE"
    echo ""
  fi

  # Expor portas
  if [ -n "$PORTS" ]; then
    echo "# Expor portas detectadas"
    for p in $PORTS; do
      pn=$(echo "$p" | sed 's/[^0-9].*$//')
      if [ -n "$pn" ] && [ "$pn" -gt 0 ] && [ "$pn" -lt 65536 ]; then
        echo "EXPOSE ${pn}"
      fi
    done
    echo ""
  fi

  # Volumes para dados persistentes
  echo "# Volumes para dados persistentes"
  echo "VOLUME [\"/var/log\", \"/var/lib/mysql\", \"/var/www\", \"/etc\"]"
  echo ""

  # Healthcheck
  echo "# Healthcheck básico"
  echo "HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\"
  echo "  CMD curl -f http://localhost/ || exit 1"
  echo ""

  # Comando de inicialização
  echo "# Script de inicialização"
  echo "COPY docker-entrypoint.sh /usr/local/bin/"
  echo "RUN chmod +x /usr/local/bin/docker-entrypoint.sh"
  echo ""
  echo "ENTRYPOINT [\"/usr/local/bin/docker-entrypoint.sh\"]"
  echo "CMD [\"/sbin/init\"]"
  
} > "$DOCKERFILE"

# -----------------------------
# Gerar Script de Entrypoint
# -----------------------------
cat > "docker-entrypoint.sh" << 'EOF'
#!/bin/bash

# Docker Entrypoint Script
# Inicializa serviços e configurações

# Iniciar cron
if command -v crond >/dev/null 2>&1; then
    crond
elif command -v cron >/dev/null 2>&1; then
    cron
fi

# Iniciar outros serviços essenciais
if command -v systemctl >/dev/null 2>&1; then
    systemctl start rsyslog 2>/dev/null || true
fi

# Executar comando passado como argumento
exec "$@"
EOF

chmod +x "docker-entrypoint.sh"

# -----------------------------
# Gerar Scripts de Backup
# -----------------------------
# Gerar script de backup dinâmico baseado nos diretórios descobertos
cat > "backup_server.sh" << EOF
#!/bin/bash

# Script para fazer backup dos diretórios importantes
# Execute este script no servidor original antes de usar o Dockerfile
# Gerado automaticamente em $(date)

BACKUP_DIR="./backup"
mkdir -p "\$BACKUP_DIR"

echo "=== Iniciando backup dos diretórios descobertos ==="
echo "Data/Hora: \$(date)"
echo ""

# Diretórios descobertos automaticamente:
EOF

# Adicionar os diretórios descobertos ao script
for dir in "${DEPLOY_DIRS[@]}"; do
    cat >> "backup_server.sh" << EOF
if [ -d "$dir" ]; then
    echo "Fazendo backup de $dir..."
    mkdir -p "\$BACKUP_DIR$dir"
    rsync -avH --exclude='*.log' --exclude='tmp' --exclude='cache' \\
          --exclude='*.pid' --exclude='*.sock' --exclude='proc' \\
          --exclude='sys' --exclude='dev' --exclude='run' \\
          "$dir/" "\$BACKUP_DIR$dir/" 2>/dev/null || true
    echo "  Backup de $dir concluído"
fi

EOF
done

cat >> "backup_server.sh" << 'EOF'
echo ""
echo "=== Backup completo ==="
echo "Diretório de backup: $BACKUP_DIR"
echo "Tamanho total: $(du -sh $BACKUP_DIR 2>/dev/null | cut -f1)"
echo "Arquivos copiados: $(find $BACKUP_DIR -type f | wc -l)"
echo ""
echo "Para usar com Docker:"
echo "1. docker build -t $CONTAINER_NAME ."
echo "2. docker run -d --name $CONTAINER_NAME $CONTAINER_NAME"
EOF

chmod +x "backup_server.sh"

# -----------------------------
# Resumo Final
# -----------------------------
echo "================================" | tee -a "$INFOFILE"
echo "ANÁLISE COMPLETA FINALIZADA" | tee -a "$INFOFILE"
echo "================================" | tee -a "$INFOFILE"
echo "Container: $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "Diretório: $(pwd)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"
echo "Arquivos gerados:" | tee -a "$INFOFILE"
echo "  - $DOCKERFILE (Dockerfile principal)" | tee -a "$INFOFILE"
echo "  - docker-entrypoint.sh (Script de inicialização)" | tee -a "$INFOFILE"
echo "  - backup_server.sh (Script de backup dinâmico)" | tee -a "$INFOFILE"
echo "  - $INFOFILE (Log da análise)" | tee -a "$INFOFILE"
echo "  - $PACKAGES_FILE (Lista completa de pacotes)" | tee -a "$INFOFILE"
echo "  - $CUSTOM_PACKAGES_FILE (Pacotes personalizados)" | tee -a "$INFOFILE"
echo "  - $SERVICES_FILE (Serviços habilitados)" | tee -a "$INFOFILE"
echo "  - $CRONTAB_FILE (Crontab do root)" | tee -a "$INFOFILE"
echo "  - $CONFIGS_FILE (Arquivos de configuração)" | tee -a "$INFOFILE"
echo "  - $APP_DIRS_FILE (Diretórios de aplicação)" | tee -a "$INFOFILE"
echo "  - $USERFILE (Usuários personalizados)" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# Estatísticas finais
if [ -s "$APP_DIRS_FILE" ]; then
    APP_COUNT=$(wc -l < "$APP_DIRS_FILE")
    echo "Estatísticas:" | tee -a "$INFOFILE"
    echo "  - Diretórios de aplicação: $APP_COUNT" | tee -a "$INFOFILE"
fi

if [ -s "$CONFIGS_FILE" ]; then
    CONFIG_COUNT=$(wc -l < "$CONFIGS_FILE")
    echo "  - Arquivos de configuração: $CONFIG_COUNT" | tee -a "$INFOFILE"
fi

if [ -s "$CUSTOM_PACKAGES_FILE" ]; then
    PACKAGE_COUNT=$(wc -l < "$CUSTOM_PACKAGES_FILE")
    echo "  - Pacotes personalizados: $PACKAGE_COUNT" | tee -a "$INFOFILE"
fi

echo "" | tee -a "$INFOFILE"
echo "PRÓXIMOS PASSOS:" | tee -a "$INFOFILE"
echo "1. Execute './backup_server.sh' para fazer backup dos dados" | tee -a "$INFOFILE"
echo "2. Construa a imagem: docker build -t $CONTAINER_NAME ." | tee -a "$INFOFILE"
echo "3. Execute o container: docker run -d --name $CONTAINER_NAME $CONTAINER_NAME" | tee -a "$INFOFILE"
echo "" | tee -a "$INFOFILE"

# Copiar crontabs para uso no Dockerfile
if [ -s "$CRONTAB_FILE" ]; then
    cp "$CRONTAB_FILE" crontabs.txt
    echo "Crontab copiada para crontabs.txt" | tee -a "$INFOFILE"
fi

echo "Script finalizado com sucesso!" | tee -a "$INFOFILE"
echo ""
echo "Todos os arquivos foram criados no diretório: $(pwd)"
echo "Execute 'cd $CONTAINER_NAME' para acessar os arquivos gerados."